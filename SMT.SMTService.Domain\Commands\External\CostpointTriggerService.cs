﻿using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SMT.Resources.Common.Entities.External;
using SMT.Resources.Common.Entities.Master;
using SMT.Resources.Common.Enums;
using SMT.Resources.Common.Helpers;
using SMT.Resources.Common.Mappers.Csv;
using SMT.Resources.Functional.Context;
using SMT.Resources.Functional.Handlers;
using SMT.Resources.Functional.Notifier.Email;
using SMT.Resources.Functional.Services;
using SMT.SMTService.DataAccess;

namespace SMT.SMTService.Domain.Commands.External
{
    public class CostpointTriggerService : ExternalStorageTriggerService<CostpointTriggerService, CostpointEntity, CostpointEntityMapper>
    {
        public CostpointTriggerService(SMTContext context, SftpStorageContext sftpContext, SMTEmailClient emailClient, IMapper mapper, ILogger<CostpointTriggerService> logger)
            : base(context, sftpContext, emailClient, mapper, logger)
        {
        }

        public override string FileNamePattern { get; set; } = SourceFilePattern.CostpointCsv;

        public override async Task EntityAttachEvent(CostpointEntity entity, EntityState state)
        {
            try
            {
                if (entity.IsContractor || entity.WorkEmail.Contains("pumex.com"))
                    return;

                if (!entity.WorkEmail.IsSercoDomain())
                    return;

                var currentEntity = await _context.Set<CostpointEntity>()
                    .FirstOrDefaultAsync(x => x.EmployeeId == entity.EmployeeId);

                entity.StatusType = PersonType.Employee.ToDescription();

                if (currentEntity == null)
                {
                    var entry = await _context.Set<CostpointEntity>().AddAsync(entity);
                    await _context.SaveChangesAsync();
                    await UpdatePersonnel(entry.Entity);
                    _logger.LogInformation($"Costpoint {entity.EmployeeId} updated.");
                    return;
                }

                entity.DateCreated = currentEntity.DateCreated;
                entity.Id = currentEntity.Id;
                await base.EntityAttachEvent(entity, EntityState.Modified);
                await UpdatePersonnel(entity);
                _logger.LogInformation($"Costpoint {entity.EmployeeId} updated.");
            }
            catch (Exception ex)
            {
                var message = $"Error when updating {entity.EmployeeId} => {ex.GetLoggerMessage()}";
                _logger.LogError(ex, message);
                throw;
            }
        }

        private async Task UpdatePersonnel(CostpointEntity entity)
        {
            var set = _context.Set<PersonnelEntity>()
                .Include(personnel => personnel.PrimaryCAGE)
                    .ThenInclude(fac => fac.FSO)
                .Include(personnel => personnel.PrimaryCAGE)
                    .ThenInclude(fac => fac.AFSOs)
                        .ThenInclude(fac => fac.Personnel)
                .Include(personnel => personnel.PrimaryDD254);

            var personnel = await set.FirstOrDefaultAsync(x => x.CostpointId == entity.Id);
            //var personnel = await _context.Set<PersonnelEntity>().FirstOrDefaultAsync(x => x.CostpointId == entity.Id);

            if (personnel == null)
            {
                personnel = await set.FirstOrDefaultAsync(x => x.EmployeeId == entity.EmployeeId);

                if (personnel == null)
                    personnel = await set.FirstOrDefaultAsync(x => x.WorkEmail.ToLower() == entity.WorkEmail.ToLower());

                if (personnel == null)
                    return;
            }

            var oriPersonnel = personnel.Copy();
            personnel.CostpointId = entity.Id;
            personnel.EmployeeId = entity.EmployeeId;
            personnel.Status = entity.Status;
            personnel.FirstName = entity.FirstName;
            personnel.LastName = entity.LastName;
            personnel.MiddleName = entity.MiddleName;
            personnel.Suffix = entity.Suffix;
            personnel.OBSBusinessUnit = entity.OBSBusinessUnit;
            personnel.EEStartDate = entity.EEStartDate;
            personnel.EETermDate = entity.EETermDate;
            personnel.PersonalEmail = entity.PersonalEmail;
            personnel.PersonalPhone = entity.PersonalPhone;
            personnel.WorkEmail = entity.WorkEmail;
            personnel.WorkPhone = entity.WorkPhone;
            personnel.PositionTitle = entity.PositionTitle;
            personnel.MaritalStatus = entity.MaritalStatus;
            personnel.ModifiedById = 1;
            personnel.DateModified = entity.DateModified;

            _changeLogger.LogChanges<PersonnelEntity>(oriPersonnel, personnel);

            if (_changeLogger.ChangeLogs.Any())
            {
                DetachTrackedEntities(personnel);
                var entry = _context.Set<PersonnelEntity>().Update(personnel);
                await _changeLogger.AttachToContext(_context);
                await _context.SaveChangesAsync();
                var email = EmailNotifier.Create(oriPersonnel, EmailNotificationType.PersonnelRecordUpdated);
                await _emailClient.Send(email);
            }
        }

        public void DetachTrackedEntities(PersonnelEntity personnel)
        {
            personnel.ModifiedBy = null;
            personnel.CreatedBy = null;
            personnel.Costpoint = null;
            personnel.PrimaryCAGE = null;
            personnel.PrimaryDD254 = null;
            personnel.SecondaryDD254s = null;
            personnel.AdditionalCAGEs = null;
        }
    }
}