﻿using AutoMapper;
using LinqKit;
using Microsoft.Azure.WebJobs.Host.Bindings;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using SMT.Resources.Common;
using SMT.Resources.Common.Domain;
using SMT.Resources.Common.Dtos.Master;
using SMT.Resources.Common.Entities.Base;
using SMT.Resources.Common.Entities.Master;
using SMT.Resources.Common.Enums;
using SMT.Resources.Common.Helpers;
using SMT.Resources.Common.Helpers.Extensions;
using SMT.Resources.Common.ListDtos.Base;
using SMT.Resources.Common.ListDtos.Master;
using SMT.Resources.Common.LookupDtos.Base;
using SMT.Resources.Common.LookupDtos.Master;
using SMT.Resources.Functional.Auth.AzureAD;
using SMT.Resources.Functional.Services;
using SMT.SMTService.DataAccess;
using System.IdentityModel.Tokens.Jwt;
using System.Linq.Expressions;
using System.Security.Claims;
using System.Text;

namespace SMT.SMTService.Domain.Queries.Master
{
    public class UserQueryService : PagedQueryService<UserQueryService, UserDto, UserListDto, UserEntity, MasterHistoryEntity, MasterHistoryDto, MasterCommentDto, MasterCommentEntity>
    {
        private const string _bearer = "Bearer";
        private readonly IConfiguration _configuration;

        public UserQueryService(SMTContext context, IMapper mapper, IConfiguration configuration, ILogger<UserQueryService> logger, IOptions<ExecutionContextOptions> executionContext)
            : base(context, mapper, logger, executionContext)
        {
            _configuration = configuration;
        }

        public override string[] SetLookupKeys() => new string[]
        {
            nameof(UserEntity.FirstName),
            nameof(UserEntity.MiddleName),
            nameof(UserEntity.LastName),
            nameof(UserEntity.Suffix),
            nameof(UserEntity.EmployeeId),
            nameof(UserEntity.StatusType)
        };

        public override string[] SetSearchKeys() => new string[]
        {
            nameof(UserListDto.FirstName),
            nameof(UserListDto.MiddleName),
            nameof(UserListDto.LastName),
            nameof(UserListDto.Suffix),
            nameof(UserListDto.EmployeeId),
            nameof(UserListDto.StatusType),
            nameof(UserListDto.FSOs),
            nameof(UserListDto.ISSMs),
            nameof(UserListDto.AFSOs),
            nameof(UserListDto.ISSMs)
        };

        public override IQueryable<UserEntity> SetCollection() => new EntityCollection(_context).GetCollection<UserEntity>()
            .Include(user => user.Personnel)
                .ThenInclude(user => user.FacilityFSO)
            .Include(user => user.Personnel)
                .ThenInclude(user => user.FacilityAFSO)
                    .ThenInclude(afso => afso.Facility)
            .Include(user => user.Personnel)
                .ThenInclude(user => user.FacilityISSM)
                    .ThenInclude(issm => issm.Facility)
            .Where(user => user.Status == UserStatusType.Active.ToDescription() || user.Status == UserStatusType.Contingent.ToDescription());

        public override async Task<PagedResponseResult<UserListDto>> Get(PagedSearch search)
        {
            Collection = Collection.Where(user => user.IsAdmin || user.Personnel.FacilityAFSO.Any() || user.Personnel.FacilityFSO.Any() || user.Personnel.FacilityISSM.Any());
            return await base.Get(search);
        }

        public override Task<PagedResponseResult<object>> LookupByKeys(string value, IDictionary<string, string> queryParams, AccessClaimFilter accessClaims, CancellationToken cancellationToken)
        {
            DefaultOrderKey = nameof(UserDto.FullName);
            Collection = _context.Set<UserEntity>().Where(user => (user.Status == UserStatusType.Active.ToDescription() || user.Status == UserStatusType.Contingent.ToDescription()) && user.Id != 1);
            return base.LookupByKeys(value, queryParams, accessClaims, cancellationToken: cancellationToken);
        }

        public override ExpressionStarter<UserEntity> BuildLookupAccessPredicate(AccessClaimFilter accessClaims, Expression<Func<UserEntity, bool>>? predicate = null)
        {
            return PredicateBuilder.New<UserEntity>(true);
        }

        public override ExpressionStarter<UserEntity> BuildLookupKeyPredicate(IDictionary<string, (string search, LookupQueryType queryType)> lookups, bool splitBySpace = false)
        {
            if (!lookups.Keys.Contains(nameof(UserLookup.FirstName)) &&
                !lookups.Keys.Contains(nameof(UserLookup.MiddleName)) &&
                !lookups.Keys.Contains(nameof(UserLookup.LastName)) &&
                !lookups.Keys.Contains(nameof(UserLookup.Suffix)))
                return base.BuildLookupKeyPredicate(lookups, true);

            var value = lookups.First().Value.search.Replace("$", "").ToLower();

            if (string.IsNullOrEmpty(value))
                return base.BuildLookupKeyPredicate(lookups, true);

            lookups = new Dictionary<string, (string search, LookupQueryType queryType)>();
            if (value.Contains(','))
            {
                var nameDivisions = value.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var lastAndSuffix = nameDivisions[0];

                var lastNameDivisions = lastAndSuffix.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                lookups[nameof(UserLookup.LastName)] = (lastNameDivisions[0].Trim(), LookupQueryType.And);
                if (lastNameDivisions.Length > 1)
                    lookups[nameof(UserLookup.Suffix)] = (lastNameDivisions[1].Trim(), LookupQueryType.And);

                if (nameDivisions.Length > 1 && !string.IsNullOrEmpty(nameDivisions[1].Trim()))
                {
                    var firstAndMiddle = nameDivisions[1];
                    var firstNameDivisions = firstAndMiddle.Split(" ", StringSplitOptions.RemoveEmptyEntries);
                    lookups[nameof(UserLookup.FirstName)] = (firstNameDivisions[0].Trim(), LookupQueryType.And);
                    if (firstNameDivisions.Length > 1)
                        lookups[nameof(UserLookup.MiddleName)] = (firstNameDivisions[1].Trim(), LookupQueryType.And);
                }
            }
            else
            {
                var nameDivisions = value.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                foreach (var nameDivision in nameDivisions)
                {
                    var name = nameDivision.Trim();
                    if (!string.IsNullOrEmpty(name))
                    {
                        lookups[nameof(UserLookup.LastName)] = (name, LookupQueryType.Or);
                        lookups[nameof(UserLookup.FirstName)] = (name, LookupQueryType.Or);
                    }
                }
            }

            var predicate = PredicateBuilder.New<UserEntity>();

            foreach (var key in lookups.Keys)
            {
                predicate = lookups[key].queryType switch
                {
                    LookupQueryType.And => predicate.And(x => EF.Functions.Like(EF.Property<string>(x, key), $"{lookups[key].search}%")),
                    _ => predicate.Or(x => EF.Functions.Like(EF.Property<string>(x, key), $"{lookups[key].search}%")),
                };
            }

            return predicate;
        }

        public override IDictionary<string, (string, LookupQueryType)> BuildLookupDictionary(string value, IDictionary<string, string> queryParams)
        {
            var dictionary = base.BuildLookupDictionary(value, queryParams);
            dictionary["Status"] = ("Active+Contingent", LookupQueryType.And);
            return dictionary;
        }

        public override ExpressionStarter<UserEntity> BuildLookupKeyPredicate(ExpressionStarter<UserEntity> predicate, string key, string lookup)
        {
            return key switch
            {
                nameof(UserLookup.Status) => predicate.Or(personnel => EF.Functions.Like(personnel.Status, lookup)),
                _ => predicate.Or(e => EF.Functions.Like(EF.Property<string>(e, key), $"{lookup}%"))
            };
        }

        public override IQueryable<object> ApplyLookupSelect(IQueryable<UserEntity> collection) => collection.Select(user => new UserLookup()
        {
            Id = user.Id,
            Status = user.Status,
            EmployeeId = user.EmployeeId,
            IsAdmin = user.IsAdmin,
            StatusType = user.StatusType,
            FirstName = user.FirstName,
            LastName = user.LastName,
            MiddleName = user.MiddleName,
            Suffix = user.Suffix,
            Email = user.Email,
            SecurityEmail = user.SecurityEmail,
            LookupValue = user.LookupValue
        });

        public override IQueryable<UserEntity> ApplyOrder(IQueryable<UserEntity> collection, BasicSearch search)
        {
            return search.OrderBy switch
            {
                nameof(UserDto.FullName) => collection.OrderAndSortByFullName(x => x, search.IsDescending),
                _ => base.ApplyOrder(collection, search)
            };
        }

        public override ExpressionStarter<UserEntity> BuildSearchKeyPredicate(ExpressionStarter<UserEntity> predicate, string key, string search)
        {
            predicate = key switch
            {
                nameof(UserListDto.FSOs) or "FSO" or "fso" => predicate.Or(user => user.Personnel.FacilityFSO.Any(x => EF.Functions.Like(x.ReferenceName, $"%{search}%"))),
                nameof(UserListDto.AFSOs) or "AFSO" or "afso" => predicate.Or(user => user.Personnel.FacilityAFSO.Any(x => EF.Functions.Like(x.Facility.ReferenceName, $"%{search}%"))),
                nameof(UserListDto.ISSMs) or "ISSM" or "issm" => predicate.Or(user => user.Personnel.FacilityISSM.Any(x => EF.Functions.Like(x.Facility.ReferenceName, $"%{search}%"))),
                _ => base.BuildSearchKeyPredicate(predicate, key, search)
            };

            return predicate;
        }

        public async Task<ResponseResult<UserDto>?> ValidateToken(string bearerToken)
        {
            try
            {
                if (bearerToken == null)
                    return null;

                if (!bearerToken.Contains(_bearer, StringComparison.OrdinalIgnoreCase))
                    return null;

                var token = bearerToken.Substring($"{_bearer} ".Length);

                if (string.IsNullOrEmpty(token))
                    return null;

                var jwt = new JwtSecurityTokenHandler().ReadJwtToken(token);

                if (jwt.ValidTo < DateTime.UtcNow)
                    return new ResponseResult<UserDto>(new Error(string.Format(Messages.ExpiredToken, jwt.ValidTo, DateTime.UtcNow), ExceptionType.AuthorizationException));

                if (!IsValidClaim(jwt.Payload))
                    return new ResponseResult<UserDto>(Error.UnauthorizedError);

                var user = await ValidateUser(jwt.Payload);

                if (user == null)
                    return new ResponseResult<UserDto>(Error.UnauthorizedError);

                if (user.Status == UserStatusType.Inactive.ToDescription() || user.Status == UserStatusType.Separated.ToDescription())
                    return new ResponseResult<UserDto>(Error.AccessRevokedError);

                return new ResponseResult<UserDto>(user);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                return new ResponseResult<UserDto>(Error.UnauthorizedError);
            }
        }

        public async Task<ResponseResult<UserDto>> GetAccess(AdClaims adClaims)
        {
            try
            {
                var email = adClaims.ClaimsPrincipal.Identity.Name;

                if (string.IsNullOrEmpty(email) || !email.IsSercoDomain())
                    email = ((ClaimsIdentity)adClaims.ClaimsPrincipal.Identity).Claims.FirstOrDefault(claim => claim.Type == "preferred_username").Value;

#if DEBUG
                adClaims.ExpiryDate = DateTime.Now.AddYears(1);
                var user = await _context.Set<UserEntity>()
                    .Include(user => user.Personnel).ThenInclude(personnel => personnel.FacilityISSM).ThenInclude(issm => issm.Facility)
                    .Include(user => user.Personnel).ThenInclude(personnel => personnel.FacilityAFSO).ThenInclude(afso => afso.Facility)
                    .Include(user => user.Personnel).ThenInclude(personnel => personnel.FacilityFSO)
                    .FirstOrDefaultAsync(user => user.Id == 5);

                email = "<EMAIL>";
#else

                UserEntity? user = null;
                // Pumex And EM Logins
                if (email.Contains("pumex.com") || AppConstants.QAModeEnabled || AppConstants.ComAdminEnabled)
                    user = await _context.Set<UserEntity>()
                        .Include(user => user.Personnel).ThenInclude(personnel => personnel.FacilityISSM).ThenInclude(issm => issm.Facility)
                        .Include(user => user.Personnel).ThenInclude(personnel => personnel.FacilityAFSO).ThenInclude(afso => afso.Facility)
                        .Include(user => user.Personnel).ThenInclude(personnel => personnel.FacilityFSO)
                        .FirstOrDefaultAsync(user => user.NormalizedSecEmail == email.ToLower() || user.NormalizedEmail == email.ToLower());
                else
                {
                    user = await _context.Set<UserEntity>()
                        .FirstOrDefaultAsync(user => user.NormalizedEmail == email.ToLower());
                    user.IsAdmin = false;
                }
#endif

                if (user == null)
                    return new ResponseResult<UserDto>(Error.UnauthorizedError);

                if (user.Status == UserStatusType.Inactive.ToDescription() || user.Status == UserStatusType.Separated.ToDescription())
                    return new ResponseResult<UserDto>(Error.AccessRevokedError);

                user.IsContracts = AppConstants.SubDD254FullAccess ? true : IsUserInContracts(adClaims.ClaimsPrincipal.Claims.Where(claim => claim.Type == "groups"));
                var smtUser = _mapper.Map<UserDto>(user);

                var claims = CreateClaims(smtUser, email);
                smtUser.AccessToken = CreateAccessToken(claims, adClaims.ExpiryDate);
                return new ResponseResult<UserDto>(smtUser);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
                return new ResponseResult<UserDto>(ex.Message);
            }
        }

        private List<Claim> CreateClaims(UserDto user, string loginEmail)
        {
            var claims = new List<Claim>()
            {
                new Claim(JwtRegisteredClaimNames.Sub, "SMT.SMTService"),
                new Claim("Id", user.Id.ToString()),
                new Claim("FullName", user.FullName),
                new Claim("Email", loginEmail.ToLowerInvariant())
            };

            if (user.ISSMs.Any())
                user.ISSMs.ForEach(cage => claims.Add(new Claim("ISSM", cage)));

            if (user.AFSOs.Any())
                user.AFSOs.ForEach(cage => claims.Add(new Claim("AFSO", cage)));

            if (user.FSOs.Any())
                user.FSOs.ForEach(cage => claims.Add(new Claim("FSO", cage)));

            claims.AddRoleClaim(user);
            return claims;
        }

        private bool IsUserInContracts(IEnumerable<Claim> adGroups)
        {
            foreach (var group in adGroups)
                if (AppConstants.ContractGroups.Contains(group.Value.ToLowerInvariant()))
                    return true;

            return false;
        }

        private string CreateAccessToken(List<Claim> claims, DateTime expiryDate)
        {
            var startAccess = DateTime.UtcNow;
            claims.Add(new Claim(JwtRegisteredClaimNames.Exp, expiryDate.Ticks.ToString()));

            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes("PdSgVkYp3s5v8y/B?E(H+MbQeThWmZq4t7w9z$C&F)J@NcRfUjXn2r5u8x/A%D*G"));
            var signInCreds = new SigningCredentials(key, SecurityAlgorithms.HmacSha512);
            var jwt = new JwtSecurityToken(issuer: "SMT.SMTService", audience: "SMT.WebApp", claims: claims, startAccess, expiryDate, signingCredentials: signInCreds);
            return new JwtSecurityTokenHandler().WriteToken(jwt);
        }

        private async Task<UserDto?> ValidateUser(JwtPayload payload)
        {
            var id = payload["Id"].ToString().ToInt32OrDefault();
            var loginEmail = payload["Email"].ToString().ToLowerInvariant();

            if (!loginEmail.IsSercoDomain())
                return null;

            var userSet = _context.Set<UserEntity>().AsQueryable();
            if (loginEmail.IsSecurityEmail())
                userSet = _context.Set<UserEntity>()
                    .Include(user => user.SecurityRoles);

            var user = await userSet.FirstOrDefaultAsync(user => user.Id == id);

            if (user == null)
                return null;

            //if (loginEmail == user.NormalizedEmail)
            //    user.IsAdmin = false;

            user.IsContracts = AppConstants.SubDD254FullAccess ? true : payload["Role"].ToString() == "Contracts";
            var userDto = _mapper.Map<UserDto>(user);
            userDto.Role = userDto.GetRoles();
            return userDto;
        }

        private bool IsValidClaim(JwtPayload payload)
        {
            if (payload["aud"]?.ToString() != "SMT.WebApp")
                return false;

            if (payload["sub"]?.ToString() != "SMT.SMTService")
                return false;

            if (payload["iss"]?.ToString() != "SMT.SMTService")
                return false;

            return true;
        }
    }
}